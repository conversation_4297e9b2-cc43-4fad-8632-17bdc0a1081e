import { OpenAI } from 'openai';
import * as FileSystem from 'expo-file-system';
import { generateUniqueItemId, parseUniversalLink } from '@/utils/qrcode';

const OPENAI_API_KEY = '********************************************************************************************************************************************************************';

export interface EntityReference {
  id: string;
  type: 'animal' | 'plant' | 'garden' | 'field' | 'equipment';
  name: string;
  summary: string;
  image?: string;
  status?: string;
  key_info?: Record<string, any>;
}

export interface EntityListing {
  type: 'animal' | 'plant' | 'garden' | 'field' | 'equipment';
  title: string;
  entities: EntityReference[];
  total_count: number;
  show_all_link?: boolean;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  images?: string[];
  timestamp: Date;
  metadata?: {
    suggestedActions?: SuggestedAction[];
    analysisData?: any;
    entityReferences?: EntityReference[];
    entityListings?: EntityListing[];
    showEntityData?: boolean;
  };
}

export interface SuggestedAction {
  id: string;
  type: 'create' | 'update' | 'delete' | 'harvest';
  entity: 'animal' | 'plant' | 'garden' | 'field' | 'equipment' | 'crop';
  title: string;
  description: string;
  data: any;
  confidence: number;
}

export class AIAssistantService {
  private static instance: AIAssistantService;

  static getInstance(): AIAssistantService {
    if (!AIAssistantService.instance) {
      AIAssistantService.instance = new AIAssistantService();
    }
    return AIAssistantService.instance;
  }

  // Helper method to get fields with active crops for harvest suggestions
  async getFieldsWithActiveCrops(context: any): Promise<EntityListing | null> {
    try {
      const fields = context.currentEntities?.filter((e: any) =>
        e.type === 'field' && e.activeCropId
      ) || [];

      if (fields.length === 0) return null;

      return {
        type: 'field',
        title: 'Fields Ready for Harvest',
        entities: fields.map((field: any) => ({
          id: field.id,
          type: 'field',
          name: field.name,
          summary: `Active crop: ${field.cropType || 'Unknown'} - Ready for harvest`,
          status: 'active',
          key_info: {
            cropType: field.cropType,
            size: field.size,
            sizeUnit: field.sizeUnit
          }
        })),
        total_count: fields.length,
        show_all_link: true
      };
    } catch (error) {
      console.error('Error getting fields with active crops:', error);
      return null;
    }
  }

  // Helper method to get available fields for planting crops
  async getAvailableFieldsForCrops(context: any): Promise<EntityListing | null> {
    try {
      const fields = context.currentEntities?.filter((e: any) =>
        e.type === 'field' && !e.activeCropId && e.status === 'active'
      ) || [];

      if (fields.length === 0) return null;

      return {
        type: 'field',
        title: 'Available Fields for Planting',
        entities: fields.map((field: any) => ({
          id: field.id,
          type: 'field',
          name: field.name,
          summary: `${field.size} ${field.sizeUnit} - Ready for planting`,
          status: 'available',
          key_info: {
            size: field.size,
            sizeUnit: field.sizeUnit,
            soilType: field.soilType
          }
        })),
        total_count: fields.length,
        show_all_link: true
      };
    } catch (error) {
      console.error('Error getting available fields:', error);
      return null;
    }
  }

  // Helper method to get gardens for adding plants
  async getGardensForPlants(context: any): Promise<EntityListing | null> {
    try {
      const gardens = context.currentEntities?.filter((e: any) =>
        e.type === 'garden' && e.status === 'active'
      ) || [];

      if (gardens.length === 0) return null;

      return {
        type: 'garden',
        title: 'Gardens Available for Planting',
        entities: gardens.map((garden: any) => ({
          id: garden.id,
          type: 'garden',
          name: garden.name,
          summary: `${garden.gardenType || garden.type} garden - ${garden.size} ${garden.sizeUnit}`,
          status: 'active',
          key_info: {
            type: garden.gardenType || garden.type,
            size: garden.size,
            sizeUnit: garden.sizeUnit
          }
        })),
        total_count: gardens.length,
        show_all_link: true
      };
    } catch (error) {
      console.error('Error getting gardens:', error);
      return null;
    }
  }

  // Helper method to get equipment by category
  async getEquipmentByCategory(context: any, category?: string): Promise<EntityListing | null> {
    try {
      let equipment = context.currentEntities?.filter((e: any) =>
        e.type === 'equipment' || e.category === 'Equipment' || e.category === 'Tools'
      ) || [];

      if (category) {
        equipment = equipment.filter((e: any) =>
          e.category?.toLowerCase() === category.toLowerCase() ||
          e.type?.toLowerCase().includes(category.toLowerCase())
        );
      }

      if (equipment.length === 0) return null;

      return {
        type: 'equipment',
        title: category ? `${category} Equipment` : 'All Equipment',
        entities: equipment.map((equip: any) => ({
          id: equip.id,
          type: 'equipment',
          name: equip.name,
          summary: `${equip.category || equip.type} - ${equip.status || 'Available'}`,
          status: equip.status || 'available',
          key_info: {
            category: equip.category,
            manufacturer: equip.manufacturer,
            model: equip.model
          }
        })),
        total_count: equipment.length,
        show_all_link: true
      };
    } catch (error) {
      console.error('Error getting equipment:', error);
      return null;
    }
  }

  async analyzeMessage(
    message: string,
    images: string[] = [],
    context: {
      farmId: string;
      currentEntities?: any[];
      userLocation?: { lat: number; lng: number };
    },
    language: string = 'en'
  ): Promise<{
    response: string;
    suggestedActions: SuggestedAction[];
    analysisData?: any;
    entityReferences?: EntityReference[];
    entityListings?: EntityListing[];
    showEntityData?: boolean;
  }> {
    try {
      // Check for special requests and provide appropriate entity listings
      const lowerMessage = message.toLowerCase();
      let specialEntityListings: EntityListing[] = [];

      // Check for harvest-related requests
      if (lowerMessage.includes('harvest') || lowerMessage.includes('ready to harvest')) {
        const harvestFields = await this.getFieldsWithActiveCrops(context);
        if (harvestFields) {
          specialEntityListings.push(harvestFields);
        }
      }

      // Check for planting crop requests
      if (lowerMessage.includes('plant crop') || lowerMessage.includes('add crop') || lowerMessage.includes('crop to field')) {
        const availableFields = await this.getAvailableFieldsForCrops(context);
        if (availableFields) {
          specialEntityListings.push(availableFields);
        }
      }

      // Check for adding plants to garden requests
      if (lowerMessage.includes('add plant') || lowerMessage.includes('plant in garden')) {
        const availableGardens = await this.getGardensForPlants(context);
        if (availableGardens) {
          specialEntityListings.push(availableGardens);
        }
      }

      // Check for equipment requests
      if (lowerMessage.includes('equipment') || lowerMessage.includes('tools') || lowerMessage.includes('machinery')) {
        let category = undefined;
        if (lowerMessage.includes('tools')) category = 'Tools';
        else if (lowerMessage.includes('machinery')) category = 'Equipment';

        const equipmentListing = await this.getEquipmentByCategory(context, category);
        if (equipmentListing) {
          specialEntityListings.push(equipmentListing);
        }
      }

      const systemPrompt = this.buildSystemPrompt(context, language);
      const userContent = await this.buildUserContent(message, images);

      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${OPENAI_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-4o',
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userContent }
          ],
          response_format: { type: 'json_object' },
          max_tokens: 1500,
          temperature: 0.7,
        }),
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error?.message || 'AI analysis failed');
      }

      // Check if response has the expected structure
      if (!data.choices || !data.choices[0] || !data.choices[0].message || !data.choices[0].message.content) {
        console.error('Invalid AI response structure:', data);
        throw new Error('Invalid response from AI service');
      }

      let result;
      try {
        result = JSON.parse(data.choices[0].message.content);
      } catch (parseError) {
        console.error('Failed to parse AI response:', data.choices[0].message.content);
        throw new Error('Invalid JSON response from AI service');
      }

      console.log('AI analysis result:', result?.response, result?.suggestedActions?.[0]?.data)

      // Combine AI-generated entity listings with special entity listings
      const combinedEntityListings = [
        ...specialEntityListings,
        ...(result?.entityListings || [])
      ];

      return {
        response: result?.response || 'I analyzed your request.',
        suggestedActions: result?.suggestedActions || [],
        analysisData: result?.analysisData,
        entityReferences: result?.entityReferences || [],
        entityListings: combinedEntityListings,
        showEntityData: result?.showEntityData || specialEntityListings.length > 0,
      };
    } catch (error) {
      console.error('AI analysis error:', error);
      throw error;
    }
  }

  // Add method to handle QR code scanning
  async handleQRCodeScan(qrData: string, context: any): Promise<{
    response: string;
    suggestedActions: SuggestedAction[];
    analysisData?: any;
    entityReferences?: EntityReference[];
    entityListings?: EntityListing[];
    showEntityData?: boolean;
  }> {
    try {
      // Try to parse as universal link first
      const parsedLink = parseUniversalLink(qrData);
      
      if (parsedLink) {
        const { itemType, itemId, farmId } = parsedLink;
        
        // Check if this is for the current farm
        if (farmId !== context.farmId) {
          return {
            response: `This QR code is for a different farm. Please scan a QR code from your current farm.`,
            analysisData: {
              imageAnalysis: "QR code scanned but belongs to different farm",
              recommendations: ["Scan QR codes only from your current farm"],
              concerns: ["Farm ID mismatch"],
              confidence: 1.0
            },
            suggestedActions: []
          };
        }

        // Find the entity in current farm data
        const entity = this.findEntityById(itemId, itemType, context);
        
        if (entity) {
          return {
            response: `Found ${itemType}: ${entity.name}. What would you like to do with this ${itemType}?`,
            analysisData: {
              imageAnalysis: `QR code scanned for existing ${itemType}: ${entity.name}`,
              recommendations: [
                `Update ${itemType} information`,
                `View ${itemType} details`,
                `Add photos or notes`,
                `Create maintenance task`
              ],
              concerns: [],
              confidence: 1.0
            },
            suggestedActions: [
              {
                id: `update_${itemType}_${Date.now()}`,
                type: "update",
                entity: itemType as any,
                title: `Update ${entity.name}`,
                description: `Modify information for this ${itemType}`,
                confidence: 0.95,
                data: { ...entity }
              }
            ]
          };
        } else {
          return {
            response: `QR code is valid but the ${itemType} was not found in your farm. It may have been deleted or moved.`,
            analysisData: {
              imageAnalysis: "Valid QR code but entity not found",
              recommendations: ["Check if entity still exists", "Verify farm selection"],
              concerns: ["Entity not found in current farm"],
              confidence: 1.0
            },
            suggestedActions: []
          };
        }
      }
      
      // If not a universal link, treat as regular message
      return await this.analyzeMessage("", [], context);
      
    } catch (error) {
      console.error('Error handling QR code:', error);
      return {
        response: "Unable to process this QR code. Please try scanning again or contact support.",
        analysisData: {
          imageAnalysis: "QR code processing failed",
          recommendations: ["Try scanning again", "Check QR code quality"],
          concerns: ["QR code processing error"],
          confidence: 0.0
        },
        suggestedActions: []
      };
    }
  }

  private findEntityById(id: string, type: string, context: any): any {
    const entities = context.currentEntities || [];
    return entities.find((entity: any) => 
      (entity.id === id || entity.identificationID === id) && 
      entity.entityType === type
    );
  }

  private buildSystemPrompt(context: any, language: string = 'en'): string {
    const languageInstruction = language === 'ur'
      ? 'Always respond in Urdu language. Use proper Urdu script and agricultural terminology.'
      : 'Always respond in English language.';

    return `You are an expert agricultural AI assistant for a farm management system with advanced image analysis capabilities.

Your role:
- Analyze user messages (text, images, or both)
- Extract detailed information from images using computer vision
- Provide helpful agricultural advice
- Suggest specific actions to update farm data with complete entity details
- Always respond in JSON format
- ${languageInstruction}

IMPORTANT: For entity data, use EXACT text values from the lookup options below. The system will automatically convert these to proper lookup IDs:

PLANT LOOKUPS:
- Plant Growth Stages (status): "seedling", "growing", "flowering", "fruiting", "harvested", "dormant"
- Plant Health Status (health): "excellent", "good", "fair", "poor"

ANIMAL LOOKUPS:
- Animal species: "cattle", "goat", "sheep", "chicken"
- Animal gender: "male", "female"
- Animal health: "excellent", "good", "fair", "poor"

Use these EXACT values to ensure proper lookup ID conversion.

Farm Context:
- Farm ID: ${context.farmId}
- Available entities: animals, plants, gardens, fields, equipment
- Current entities count: ${context.currentEntities?.length || 0}

ENTITY DATA DISPLAY CAPABILITIES:
You can now include interactive entity data in your responses to help uneducated users easily browse and access their farm information.

WHEN TO SHOW ENTITY DATA:
- User asks "show me my animals/plants/fields/gardens/equipment"
- User asks about specific entities by name or ID
- User wants to browse or see their farm data
- User asks general questions about their farm status
- User needs help finding or managing their entities
- User wants to add crops to fields or plants to gardens
- User wants to harvest crops from fields
- User wants to see equipment by category

SPECIAL ACTIONS YOU CAN SUGGEST:
1. "harvest_crop_from_field" - When user wants to harvest from a specific field
2. "add_crop_to_field" - When user wants to plant crops in a field
3. "add_plant_to_garden" - When user wants to add plants to a garden
4. "load_equipment_by_category" - When user wants to see equipment of specific type

When showing entity data:
1. Set "showEntityData": true in your response
2. Include relevant "entityReferences" for specific entities mentioned
3. Include "entityListings" for showing lists of entities
4. Always provide helpful context in your text response

Entity Reference Format (for specific entities):
{
  "id": "entity_id",
  "type": "animal|plant|garden|field|equipment",
  "name": "Entity Name",
  "summary": "Brief description with key info",
  "image": "image_url_if_available",
  "status": "current_status",
  "key_info": {
    "key1": "value1",
    "key2": "value2"
  }
}

Entity Listing Format (for showing lists):
{
  "type": "animal|plant|garden|field|equipment",
  "title": "Display title for the list",
  "entities": [array of EntityReference objects],
  "total_count": total_number_of_entities,
  "show_all_link": true/false
}

Current Farm Entities Summary:
${this.buildEntitySummary(context.currentEntities || [])}

AVAILABLE ENTITIES FOR REFERENCE:
${this.buildEntityReferencesText(context.currentEntities || [])}

Entity Requirements:
ANIMAL: species (required), breed, gender, status, purpose, name, weight, weightUnit, dateOfBirth, identificationNumber, fieldId, notes
PLANT: name (required), species (required), variety, plantedDate (required), status, health, gardenId OR fieldId, notes, expectedHarvestDate
GARDEN: name (required), type (required), size, sizeUnit, status, soilType, irrigationSystem, notes
FIELD: name (required), type (required), size, sizeUnit, status, cropType, plantedDate, harvestDate, soilType, notes
EQUIPMENT: name (required), category (required - Electronics/Tools/Equipment), type (required), manufacturer, model, status, purchaseDate, purchasePrice, lastMaintenanceDate, notes
CROP: cropType (required), fieldId (required), status, plantedDate, expectedHarvestDate, soilType, notes

IMPORTANT LOOKUP REQUIREMENTS:
- For sizeUnit, use ONLY these values: "acres", "hectares", "square_meters", "square_feet", "marla_pk", "kanal_pk"
- For field type, use ONLY: "cropland", "orchard", "livestock", "garden"
- For equipment category, use ONLY: "Electronics", "Tools", "Equipment"
- For plant status, use ONLY: "seedling", "growing", "flowering", "fruiting", "harvested", "dormant"
- For plant health, use ONLY: "excellent", "good", "fair", "poor"
- For equipment status, use ONLY: "operational", "maintenance", "repair", "retired"

IDENTIFICATION ID GENERATION:
- Always include identificationID in suggested data for new entities
- Use format: ANM-XXXX for animals, PLT-XXXX for plants, GRD-XXXX for gardens, FLD-XXXX for fields, EQP-XXXX for equipment
- Generate unique 4-digit numbers (1000-9999 range)
- Example: "identificationID": "PLT-${Math.floor(1000 + Math.random() * 9000)}"

IMAGE ANALYSIS GUIDELINES:

For ANIMAL images, identify and extract:
- Species (cattle, sheep, goat, pig, chicken, etc.)
- Breed (Holstein, Angus, Merino, Yorkshire, Rhode Island Red, etc.)
- Gender (male/female based on physical characteristics)
- Age estimate (calf/juvenile/adult/senior based on size and features)
- Health status (healthy/sick/injured based on posture, eyes, coat condition)
- Body condition (thin/ideal/overweight based on visible body shape)
- Physical markings or distinctive features
- Estimated weight range based on size and breed

For PLANT images, identify and extract:
- Species (tomato, wheat, corn, apple, etc.)
- Variety (Roma tomato, Cherry tomato, Winter wheat, etc.)
- Growth stage (seedling/vegetative/flowering/fruiting/mature)
- Health status (healthy/stressed/diseased/pest damage)
- Specific diseases (blight, rust, powdery mildew, etc.)
- Nutrient deficiencies (nitrogen, phosphorus, potassium signs)
- Pest damage indicators
- Estimated planting date based on growth stage
- Expected harvest timeframe

For GARDEN images, identify and extract:
- Garden type (vegetable/herb/flower/mixed/greenhouse)
- Layout style (raised beds/rows/container/vertical)
- Plant varieties visible
- Soil condition (dry/moist/waterlogged/well-drained)
- Irrigation system type (drip/sprinkler/manual/none visible)
- Overall maintenance level
- Size estimation from visual cues
- Location indicators (indoor/outdoor/greenhouse)

For FIELD images, identify and extract:
- Crop type (wheat/corn/soybean/pasture/etc.)
- Field size estimation from perspective
- Crop growth stage and coverage density
- Soil condition (tilled/planted/harvested/fallow)
- Irrigation evidence (pivot systems/channels/dry farming)
- Terrain type (flat/sloped/terraced)
- Weed presence and severity
- Disease or pest damage signs
- Harvest readiness indicators

For EQUIPMENT images, identify and extract:
- Equipment type (tractor/harvester/plow/sprayer/cultivator/etc.)
- Manufacturer/brand (John Deere/Case IH/New Holland/etc.)
- Model identification from visible markings
- Size category (compact/utility/large/industrial)
- Condition assessment (excellent/good/fair/poor/needs repair)
- Visible wear, damage, or missing parts
- Age estimation from design and condition
- Attachments or implements visible
- Maintenance indicators (rust/paint condition/tire wear)

Response Format (JSON):
{
  "response": "Your helpful response to the user with specific details from image analysis",
  "showEntityData": false, // Set to true when including entity data for user browsing
  "entityReferences": [
    // Include when referencing specific entities in your response
    {
      "id": "entity_id",
      "type": "animal|plant|garden|field|equipment",
      "name": "Entity Name",
      "summary": "Brief description with key info",
      "image": "image_url_if_available",
      "status": "current_status",
      "key_info": {
        "species": "value",
        "health": "value"
      }
    }
  ],
  "entityListings": [
    // Include when showing lists of entities for browsing
    {
      "type": "animal|plant|garden|field|equipment",
      "title": "Your Animals" or "Recent Plants" etc,
      "entities": [array of entity references],
      "total_count": 10,
      "show_all_link": true
    }
  ],
  "analysisData": {
    "imageAnalysis": "Detailed description of what you see in images with specific identifications",
    "recommendations": ["Specific actionable recommendations based on analysis"],
    "concerns": ["Any issues, diseases, or problems identified"],
    "confidence": "Overall confidence in image analysis (0-1)"
  },
  "suggestedActions": [
    {
      "id": "unique_id_based_on_entity_and_timestamp",
      "type": "create|update",
      "entity": "animal|plant|garden|field|equipment",
      "title": "Descriptive action title with specific details",
      "description": "What this action will do with identified specifics",
      "confidence": 0.85,
      "data": {
        // ALWAYS include identificationID for create actions
        "identificationID": "PLT-${Math.floor(1000 + Math.random() * 9000)}",
        // Complete entity data with all required fields
        // Use current date for plantedDate if creating new plants
        // Use ISO date strings for all dates
        // Provide specific names like "Holstein Cow #1" or "Tomato Plant - Roma Variety"
      }
    }
  ]
}

SPECIFIC EXTRACTION RULES:
1. For animals: Always try to identify breed-specific characteristics, estimate age from size/features, assess health from posture/eyes/coat
2. For plants: Look for leaf shape, flower/fruit characteristics, growth patterns to identify species and variety
3. For equipment: Check for brand logos, model numbers, design features that indicate manufacturer and type
4. For fields/gardens: Assess crop density, growth uniformity, soil visibility, irrigation infrastructure
5. Use your agricultural knowledge to provide realistic estimates for weights, sizes, dates, and conditions
6. When uncertain, indicate confidence level and provide best estimates with appropriate caveats
7. Always include specific, actionable recommendations based on what you observe

Guidelines:
- ALWAYS include identificationID field for create actions
- Generate realistic identification IDs using the specified format
- Include ALL mandatory fields for each entity type
- Use proper data types (dates as ISO strings, numbers as numbers)
- Generate meaningful names that include species/breed/variety information`;
  }

  private buildEntitySummary(entities: any[]): string {
    if (!entities || entities.length === 0) {
      return "No entities found in current farm.";
    }

    const summary = {
      animals: entities.filter(e => e.species || e.breed).length,
      plants: entities.filter(e => e.species && (e.plantedDate || e.variety)).length,
      gardens: entities.filter(e => e.type && e.size && !e.species).length,
      fields: entities.filter(e => e.name && e.size && !e.species && !e.type?.includes('garden')).length,
      equipment: entities.filter(e => e.manufacturer || e.model || e.type?.includes('equipment')).length
    };

    return `Animals: ${summary.animals}, Plants: ${summary.plants}, Gardens: ${summary.gardens}, Fields: ${summary.fields}, Equipment: ${summary.equipment}`;
  }

  private buildEntityReferencesText(entities: any[]): string {
    if (!entities || !Array.isArray(entities) || entities.length === 0) {
      return "No entities available.";
    }

    try {
      const entityGroups = {
        animals: entities.filter(e => e && (e.species || e.breed)),
        plants: entities.filter(e => e && e.species && (e.plantedDate || e.variety)),
        gardens: entities.filter(e => e && e.type && e.size && !e.species),
        fields: entities.filter(e => e && e.name && e.size && !e.species && !e.type?.includes('garden')),
        equipment: entities.filter(e => e && (e.manufacturer || e.model || e.type?.includes('equipment')))
      };

      let text = "";

      Object.entries(entityGroups).forEach(([type, items]) => {
        if (items && items.length > 0) {
          text += `\n${type.toUpperCase()}:\n`;
          items.slice(0, 5).forEach(item => {
            if (item) {
              const name = item.name || `${item.species || item.type || 'Unknown'} ${item.id?.slice(-4) || ''}`;
              const status = item.status || item.health || 'unknown';
              text += `- ${name} (ID: ${item.id || 'N/A'}, Status: ${status})\n`;
            }
          });
          if (items.length > 5) {
            text += `... and ${items.length - 5} more\n`;
          }
        }
      });

      return text || "No entities found.";
    } catch (error) {
      console.error('Error building entity references text:', error);
      return "Error loading entity data.";
    }
  }

  private async buildUserContent(message: string, images: string[]): Promise<any[]> {
    const content: any[] = [
      { type: 'text', text: message || '' }
    ];

    // Ensure images is an array
    const imageArray = Array.isArray(images) ? images : [];

    for (const imageUri of imageArray) {
      try {
        if (!imageUri || typeof imageUri !== 'string') {
          console.warn('Invalid image URI:', imageUri);
          continue;
        }

        const base64Image = await FileSystem.readAsStringAsync(imageUri, {
          encoding: FileSystem.EncodingType.Base64,
        });

        if (base64Image) {
          content.push({
            type: 'image_url',
            image_url: { url: `data:image/jpeg;base64,${base64Image}` }
          });
        }
      } catch (error) {
        console.error('Error processing image:', imageUri, error);
      }
    }

    return content;
  }
}


