import { useLookupStore } from '@/store/lookup-store';

/**
 * Utility functions for validating and ensuring data consistency with lookup values
 */

// Entity-specific lookup category mappings
export const ENTITY_LOOKUP_MAPPINGS = {
  plant: {
    status: 'plantStatus',
    health: 'plantHealthStatus',
    species: 'plantSpecies',
    variety: 'plantVariety'
  },
  animal: {
    species: 'animalSpecies',
    breed: 'animalBreed',
    gender: 'animalGender',
    status: 'animalHealthStatus',
    purpose: 'animalPurpose'
  },
  field: {
    type: 'fieldType',
    status: 'fieldStatus',
    soilType: 'soilType',
    irrigationSystem: 'irrigationSystemStatus',
    sizeUnit: 'sizeUnit'
  },
  garden: {
    type: 'gardenType',
    soilType: 'soilType',
    irrigationSystem: 'irrigationSystemStatus',
    sizeUnit: 'sizeUnit'
  },
  task: {
    priority: 'taskPriority',
    category: 'taskCategory',
    frequency: 'taskFrequency',
    status: 'taskStatus',
    entityType: 'entityType'
  },
  equipment: {
    type: 'equipmentType',
    status: 'equipmentStatus',
    condition: 'equipmentCondition',
    category: 'equipmentCategory'
  },
  inventory: {
    category: 'inventoryCategory',
    unit: 'inventoryUnit',
    status: 'inventoryStatus'
  },
  machinery: {
    type: 'machineryType',
    status: 'machineryStatus',
    condition: 'machineryCondition',
    manufacturer: 'machineryManufacturer'
  },
  crop: {
    cropType: 'plantSpecies', // Reuse plant species for crop types
    status: 'plantStatus',
    season: 'season',
    yieldUnit: 'yieldUnit',
    soilType: 'soilType'
  }
} as const;

/**
 * Validates and sanitizes entity data before saving to Firestore
 * Ensures only valid lookup IDs are saved, but allows text fallback for species/variety
 */
export const validateEntityData = (entityType: keyof typeof ENTITY_LOOKUP_MAPPINGS, data: any) => {
  const { validateLookupId, getLookupValueById } = useLookupStore.getState();
  const lookupMappings = ENTITY_LOOKUP_MAPPINGS[entityType];
  const sanitizedData = { ...data };
  const validationErrors: string[] = [];

  // Fields that can fallback to text if no lookup match found
  const textFallbackFields = ['species', 'variety'];

  // Validate each lookup field
  Object.entries(lookupMappings).forEach(([fieldName, lookupCategory]) => {
    const fieldValue = sanitizedData[fieldName];

    if (fieldValue) {
      // If it's an object with an id property, extract the id
      const id = typeof fieldValue === 'object' && fieldValue.id ? fieldValue.id : fieldValue;

      if (typeof id === 'string') {
        // Validate the ID exists in the lookup category
        if (validateLookupId(lookupCategory, id)) {
          // Save only the ID
          sanitizedData[fieldName] = id;
        } else {
          // Check if this field can fallback to text
          if (textFallbackFields.includes(fieldName)) {
            // Keep the original text value for species/variety
            sanitizedData[fieldName] = fieldValue;
            console.log(`Using text fallback for ${fieldName}: "${fieldValue}"`);
          } else {
            validationErrors.push(`Invalid ${fieldName}: ${id} not found in ${lookupCategory}`);
            // Remove invalid field
            delete sanitizedData[fieldName];
          }
        }
      } else if (fieldValue !== null && fieldValue !== undefined && fieldValue !== '') {
        validationErrors.push(`Invalid ${fieldName} format: expected string ID, got ${typeof fieldValue}`);
        delete sanitizedData[fieldName];
      }
    }
  });

  return {
    sanitizedData,
    validationErrors,
    isValid: validationErrors.length === 0
  };
};

/**
 * Gets the display value for a lookup ID
 */
export const getLookupDisplayValue = (entityType: keyof typeof ENTITY_LOOKUP_MAPPINGS, fieldName: string, id: string) => {
  const { getLookupValueById } = useLookupStore.getState();
  const lookupMappings = ENTITY_LOOKUP_MAPPINGS[entityType];
  const lookupCategory = lookupMappings[fieldName as keyof typeof lookupMappings];
  
  if (lookupCategory) {
    const lookupItem = getLookupValueById(lookupCategory, id);
    return lookupItem?.title || id;
  }
  
  return id;
};

/**
 * Validates a single lookup field
 */
export const validateLookupField = (lookupCategory: string, value: any): { isValid: boolean; id: string | null; error?: string } => {
  const { validateLookupId } = useLookupStore.getState();
  
  if (!value) {
    return { isValid: true, id: null };
  }
  
  // Extract ID from value
  const id = typeof value === 'object' && value.id ? value.id : value;
  
  if (typeof id !== 'string') {
    return { 
      isValid: false, 
      id: null, 
      error: `Invalid format: expected string ID, got ${typeof id}` 
    };
  }
  
  if (!validateLookupId(lookupCategory, id)) {
    return { 
      isValid: false, 
      id: null, 
      error: `ID ${id} not found in ${lookupCategory}` 
    };
  }
  
  return { isValid: true, id };
};

/**
 * Converts text values to lookup IDs by finding the best match
 * This is especially useful for AI-generated data that comes as text
 */
export const convertTextToLookupId = (lookupCategory: string, textValue: string): string | null => {
  const { getLookupsByCategory } = useLookupStore.getState();
  const lookups = getLookupsByCategory(lookupCategory);
  
  if (!textValue || !lookups.length) return null;
  
  const normalizedText = textValue.toLowerCase().trim();
  
  // First try exact match on title
  let match = lookups.find((item: any) => 
    item.title?.toLowerCase() === normalizedText
  );
  
  if (match) return match.id;
  
  // Try partial match on title
  match = lookups.find((item: any) => 
    item.title?.toLowerCase().includes(normalizedText) ||
    normalizedText.includes(item.title?.toLowerCase())
  );
  
  if (match) return match.id;
  
  // Try match on common synonyms/variations
  const synonymMap: { [key: string]: string[] } = {
    // Plant Growth Stages (exact lookup values)
    'seedling': ['seed', 'germinating', 'sprouting'],
    'growing': ['active', 'vegetative', 'thriving', 'developing'],
    'flowering': ['blooming', 'in bloom', 'blossoming'],
    'fruiting': ['bearing fruit', 'producing', 'ripening'],
    'harvested': ['ready', 'mature', 'harvesting', 'picked'],
    'dormant': ['inactive', 'resting', 'sleeping'],

    // Plant Health Status (exact lookup values)
    'excellent': ['perfect', 'great', 'outstanding', 'superb'],
    'good': ['healthy', 'fine', 'well', 'normal'],
    'fair': ['okay', 'average', 'moderate', 'decent'],
    'poor': ['bad', 'sick', 'unhealthy', 'weak'],

    // Size Units
    'acres': ['acre', 'ac', 'acre_us', 'acre_pk'],
    'hectares': ['hectare', 'ha'],
    'square_meters': ['sq_m', 'sqm', 'm²', 'square meters'],
    'square_feet': ['sq_ft', 'sqft', 'ft²', 'square feet'],
    'marla_pk': ['marla', 'marlas'],
    'kanal_pk': ['kanal', 'kanals'],

    // Equipment Categories
    'electronics': ['electronic', 'electrical', 'digital'],
    'tools': ['tool', 'hand tools', 'manual tools'],
    'equipment': ['equipments', 'machinery', 'implements'],

    // Animal species synonyms
    'cattle': ['cow', 'bull', 'beef', 'dairy'],
    'goat': ['goats'],
    'sheep': ['lamb'],
    'chicken': ['poultry', 'hen', 'rooster'],

    // Gender synonyms
    'male': ['m', 'boy'],
    'female': ['f', 'girl'],
  };
  
  // Check synonyms
  for (const [key, synonyms] of Object.entries(synonymMap)) {
    if (synonyms.includes(normalizedText) || normalizedText === key) {
      match = lookups.find((item: any) => 
        item.title?.toLowerCase() === key
      );
      if (match) return match.id;
    }
  }
  
  return null;
};

/**
 * Converts AI-generated entity data with text values to proper lookup IDs
 */
export const convertAIDataToLookupIds = (entityType: keyof typeof ENTITY_LOOKUP_MAPPINGS, data: any) => {
  const lookupMappings = ENTITY_LOOKUP_MAPPINGS[entityType];
  const convertedData = { ...data };
  const conversionLog: string[] = [];
  
  Object.entries(lookupMappings).forEach(([fieldName, lookupCategory]) => {
    const fieldValue = convertedData[fieldName];
    
    if (fieldValue && typeof fieldValue === 'string') {
      const lookupId = convertTextToLookupId(lookupCategory, fieldValue);
      
      if (lookupId) {
        convertedData[fieldName] = lookupId;
        conversionLog.push(`Converted ${fieldName}: "${fieldValue}" → ${lookupId}`);
      } else {
        conversionLog.push(`Could not convert ${fieldName}: "${fieldValue}" (no match found in ${lookupCategory})`);
      }
    }
  });
  
  return {
    convertedData,
    conversionLog
  };
};
