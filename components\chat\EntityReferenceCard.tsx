import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
} from 'react-native';
import { router } from 'expo-router';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/i18n/useTranslation';
import { EntityReference } from '@/services/ai-assistant';
import { 
  Cow, 
  Sprout, 
  TreePine, 
  Tractor, 
  MapPin 
} from 'lucide-react-native';

interface EntityReferenceCardProps {
  entity: EntityReference;
  style?: any;
}

export const EntityReferenceCard: React.FC<EntityReferenceCardProps> = ({
  entity,
  style,
}) => {
  const { t, isRTL } = useTranslation();

  const getEntityIcon = (type: string) => {
    const iconProps = { size: 20, color: colors.primary };
    switch (type) {
      case 'animal':
        return <Cow {...iconProps} />;
      case 'plant':
        return <Sprout {...iconProps} />;
      case 'garden':
        return <TreePine {...iconProps} />;
      case 'field':
        return <MapPin {...iconProps} />;
      case 'equipment':
        return <Tractor {...iconProps} />;
      default:
        return <MapPin {...iconProps} />;
    }
  };

  const getStatusColor = (status?: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
      case 'healthy':
      case 'excellent':
      case 'good':
        return colors.success;
      case 'inactive':
      case 'poor':
      case 'sick':
        return colors.danger;
      case 'fair':
      case 'maintenance':
        return colors.warning;
      default:
        return colors.gray[500];
    }
  };

  const handlePress = () => {
    // Navigate to entity detail screen
    const routes = {
      animal: `/animal/${entity.id}`,
      plant: `/plant/${entity.id}`,
      garden: `/garden/${entity.id}`,
      field: `/field/${entity.id}`,
      equipment: `/equipment/${entity.id}`,
    };
    
    const route = routes[entity.type];
    if (route) {
      router.push(route);
    }
  };

  return (
    <TouchableOpacity
      style={[styles.card, style, isRTL && styles.cardRtl]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <View style={[styles.content, isRTL && styles.contentRtl]}>
        {/* Image or Icon */}
        <View style={styles.imageContainer}>
          {entity.image ? (
            <Image
              source={{ uri: entity.image }}
              style={styles.image}
              resizeMode="cover"
            />
          ) : (
            <View style={styles.iconContainer}>
              {getEntityIcon(entity.type)}
            </View>
          )}
        </View>

        {/* Content */}
        <View style={styles.textContent}>
          <View style={styles.header}>
            <Text style={[styles.name, isRTL && styles.textRtl]} numberOfLines={1}>
              {entity.name}
            </Text>
            {entity.status && (
              <View style={[
                styles.statusBadge,
                { backgroundColor: getStatusColor(entity.status) + '20' }
              ]}>
                <Text style={[
                  styles.statusText,
                  { color: getStatusColor(entity.status) }
                ]}>
                  {entity.status}
                </Text>
              </View>
            )}
          </View>

          <Text style={[styles.summary, isRTL && styles.textRtl]} numberOfLines={2}>
            {entity.summary}
          </Text>

          {/* Key Info */}
          {entity.key_info && Object.keys(entity.key_info).length > 0 && (
            <View style={[styles.keyInfo, isRTL && styles.keyInfoRtl]}>
              {Object.entries(entity.key_info).slice(0, 2).map(([key, value]) => (
                <Text key={key} style={[styles.keyInfoText, isRTL && styles.textRtl]}>
                  {key}: {value}
                </Text>
              ))}
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: colors.white,
    borderRadius: 12,
    marginVertical: 4,
    marginHorizontal: 8,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  cardRtl: {
    flexDirection: 'row-reverse',
  },
  content: {
    flexDirection: 'row',
    padding: 8, // Further reduced padding
    alignItems: 'center',
    height: 80, // Fixed height instead of minHeight
    overflow: 'hidden', // Prevent content overflow
  },
  contentRtl: {
    flexDirection: 'row-reverse',
  },
  imageContainer: {
    marginRight: 12,
  },
  image: {
    width: 40, // Smaller for compact layout
    height: 40,
    borderRadius: 6,
  },
  iconContainer: {
    width: 40, // Smaller for compact layout
    height: 40,
    borderRadius: 6,
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
  },
  textContent: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  name: {
    fontSize: 14, // Smaller font for compact layout
    fontWeight: '600',
    color: colors.gray[800],
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    marginLeft: 8,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  summary: {
    fontSize: 12, // Smaller font for compact layout
    color: colors.gray[600],
    marginBottom: 4, // Reduced margin
    lineHeight: 16,
  },
  keyInfo: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  keyInfoRtl: {
    flexDirection: 'row-reverse',
  },
  keyInfoText: {
    fontSize: 12,
    color: colors.gray[500],
    marginRight: 12,
    marginBottom: 2,
  },
  textRtl: {
    textAlign: 'right',
  },
});

export default EntityReferenceCard;
